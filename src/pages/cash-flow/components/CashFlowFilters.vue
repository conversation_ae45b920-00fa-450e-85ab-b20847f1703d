<template>
  <div class="flex items-center gap-4">
    <!-- Enhanced Date Range Picker -->
    <div class="flex-1">
      <DateRangePicker v-model="dateRange" placeholder="<PERSON><PERSON>n kho<PERSON>ng thời gian" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch } from "vue";
import { useCashFlowFilters } from "@/composables/useCashFlowFilters";
import DateRangePicker from "@/components/DateRangePicker.vue";

const emit = defineEmits<{
  (e: "filters-changed"): void;
}>();

const { dateRange, resetFilters } = useCashFlowFilters();

// Watch for external filter changes
watch(
  dateRange,
  () => {
    emit("filters-changed");
  },
  { deep: true },
);

defineExpose({
  resetFilters,
});
</script>
