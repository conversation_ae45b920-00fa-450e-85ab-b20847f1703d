import { ref, computed, watch, onMounted } from 'vue'
import dayjs from 'dayjs'

export type DateRangePreset = {
  key: string
  label: string
  getRange: () => Date[]
}

export interface UseDateRangePickerOptions {
  initialValue?: Date[] | null
  placeholder?: string
  onSelectionChange?: (value: Date[] | null) => void
}

export function useDateRangePicker(options: UseDateRangePickerOptions = {}) {
  const {
    initialValue = null,
    placeholder = 'Chọn khoảng thời gian',
    onSelectionChange
  } = options

  // State
  const panel = ref()
  const internalValue = ref<Date[] | null>(initialValue)
  const activePreset = ref<string | null>(null)
  const isOpen = ref(false)

  // Preset configurations
  const presets = ref<DateRangePreset[]>([
    {
      key: 'today',
      label: 'Hôm nay',
      getRange: () => {
        const today = new Date()
        return [today, today]
      }
    },
    {
      key: 'yesterday',
      label: 'Hôm qua',
      getRange: () => {
        const yesterday = dayjs().subtract(1, 'day').toDate()
        return [yesterday, yesterday]
      }
    },
    {
      key: 'last_7_days',
      label: '7 ngày qua',
      getRange: () => {
        const end = new Date()
        const start = dayjs().subtract(6, 'day').toDate()
        return [start, end]
      }
    },
    {
      key: 'last_30_days',
      label: '30 ngày qua',
      getRange: () => {
        const end = new Date()
        const start = dayjs().subtract(29, 'day').toDate()
        return [start, end]
      }
    },
    {
      key: 'this_month',
      label: 'Tháng này',
      getRange: () => {
        const start = dayjs().startOf('month').toDate()
        const end = new Date()
        return [start, end]
      }
    },
    {
      key: 'last_month',
      label: 'Tháng trước',
      getRange: () => {
        const start = dayjs().subtract(1, 'month').startOf('month').toDate()
        const end = dayjs().subtract(1, 'month').endOf('month').toDate()
        return [start, end]
      }
    }
  ])

  // Computed properties
  const buttonLabel = computed(() => {
    if (!internalValue.value || !internalValue.value[0] || !internalValue.value[1]) {
      return placeholder
    }
    
    return formatDateRange(internalValue.value)
  })

  const hasSelection = computed(() => {
    return internalValue.value && internalValue.value[0] && internalValue.value[1]
  })

  const hasValidSelection = computed(() => {
    return internalValue.value && internalValue.value[0] && internalValue.value[1]
  })

  // Utility functions
  function formatDateRange(dates: Date[]): string {
    if (!dates || dates.length !== 2 || !dates[0] || !dates[1]) {
      return placeholder
    }
    
    const [start, end] = dates
    const startStr = dayjs(start).format('DD/MM/YYYY')
    
    // Check if same day
    if (dayjs(start).isSame(dayjs(end), 'day')) {
      return startStr
    }
    
    const endStr = dayjs(end).format('DD/MM/YYYY')
    return `${startStr} - ${endStr}`
  }

  // Panel controls
  function togglePanel(event: Event) {
    if (!panel.value) return
    
    panel.value.toggle(event)
    isOpen.value = !isOpen.value
    
    // Reset internal state when opening
    if (isOpen.value) {
      updateActivePreset()
    }
  }

  function closePanel() {
    if (!panel.value) return
    
    panel.value.hide()
    isOpen.value = false
  }

  function handlePanelHide() {
    isOpen.value = false
    activePreset.value = null
  }

  // Preset handling
  function applyPreset(preset: DateRangePreset) {
    const range = preset.getRange()
    internalValue.value = range
    activePreset.value = preset.key
  }

  function updateActivePreset() {
    if (!internalValue.value) {
      activePreset.value = null
      return
    }
    
    // Try to match current selection to a preset
    for (const preset of presets.value) {
      const presetRange = preset.getRange()
      if (
        dayjs(internalValue.value[0]).isSame(dayjs(presetRange[0]), 'day') &&
        dayjs(internalValue.value[1]).isSame(dayjs(presetRange[1]), 'day')
      ) {
        activePreset.value = preset.key
        return
      }
    }
    
    // No preset match, must be custom
    activePreset.value = null
  }

  // Selection handling
  function applySelection() {
    if (!hasValidSelection.value) return
    
    onSelectionChange?.(internalValue.value)
    closePanel()
  }

  function cancelSelection() {
    // Reset to external value
    internalValue.value = initialValue
    activePreset.value = null
    closePanel()
  }

  // External value updates
  function updateValue(newValue: Date[] | null) {
    internalValue.value = newValue ? [...newValue] : null
    
    // Update active preset if panel is not open
    if (!isOpen.value) {
      updateActivePreset()
    }
  }

  // Watch for internal value changes to detect custom selections
  watch(internalValue, () => {
    if (internalValue.value && internalValue.value[0] && internalValue.value[1]) {
      updateActivePreset()
    }
  }, { deep: true })

  // Initialize
  onMounted(() => {
    if (initialValue) {
      internalValue.value = [...initialValue]
      updateActivePreset()
    }
  })

  return {
    // State
    panel,
    internalValue,
    activePreset,
    isOpen,
    presets,
    
    // Computed
    buttonLabel,
    hasSelection,
    hasValidSelection,
    
    // Methods
    togglePanel,
    closePanel,
    handlePanelHide,
    applyPreset,
    updateActivePreset,
    applySelection,
    cancelSelection,
    updateValue,
    formatDateRange
  }
}