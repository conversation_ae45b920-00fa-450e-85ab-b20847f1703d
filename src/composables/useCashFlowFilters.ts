import { ref, computed, reactive } from "vue";
import { transformDateRangeForDateOnlyFilter } from "@/utils/time-helper";

export interface CashFlowFilters {
  type?: "income" | "expense";
  state?: "PENDING" | "APPROVED" | "REJECTED" | "PAID" | "CANCELED";
  from_date?: string;
  to_date?: string;
}

// Shared state for cash flow filters - singleton pattern
const sharedState = reactive({
  typeFilter: null as "income" | "expense" | null,
  stateFilter: null as "PENDING" | "APPROVED" | "REJECTED" | "PAID" | "CANCELED" | null,
  dateRange: null as Date[] | null,
});

// Initialize with default date range (today)
const initializeDefaultDateRange = () => {
  if (!sharedState.dateRange) {
    const today = new Date();
    sharedState.dateRange = [today, today];
  }
};

export function useCashFlowFilters() {
  // Initialize default date range on first use
  initializeDefaultDateRange();

  // Build filters object for API calls
  const buildFilters = (): CashFlowFilters => {
    const filters: CashFlowFilters = {};

    if (sharedState.typeFilter) {
      filters.type = sharedState.typeFilter;
    }

    if (sharedState.stateFilter) {
      filters.state = sharedState.stateFilter;
    }

    if (sharedState.dateRange && sharedState.dateRange.length === 2) {
      const dateFilter = transformDateRangeForDateOnlyFilter(sharedState.dateRange);
      if (dateFilter) {
        filters.from_date = dateFilter.from;
        filters.to_date = dateFilter.to;
      }
    }

    return filters;
  };

  // Current filter status display
  const currentFilterStatus = computed(() => {
    const filters = [];

    if (sharedState.typeFilter === "income") {
      filters.push("Thu nhập");
    } else if (sharedState.typeFilter === "expense") {
      filters.push("Chi phí");
    }

    if (sharedState.stateFilter === "PENDING") {
      filters.push("Chờ phê duyệt");
    } else if (sharedState.stateFilter === "APPROVED") {
      filters.push("Đã phê duyệt");
    } else if (sharedState.stateFilter === "PAID") {
      filters.push("Đã thanh toán");
    }

    if (sharedState.dateRange && sharedState.dateRange.length === 2) {
      const [startDate, endDate] = sharedState.dateRange;

      if (startDate && endDate) {
        const formatDate = (date: Date) =>
          date.toLocaleDateString("vi-VN", { day: "2-digit", month: "2-digit" });

        if (startDate.toDateString() === endDate.toDateString()) {
          filters.push(`Ngày ${formatDate(startDate)}`);
        } else {
          filters.push(`${formatDate(startDate)} - ${formatDate(endDate)}`);
        }
      }
    }

    return filters.length > 0 ? filters.join(" • ") : "";
  });

  // Filter update handlers
  const updateTypeFilter = (type: "income" | "expense" | null) => {
    sharedState.typeFilter = type;
  };

  const updateStateFilter = (state: "PENDING" | "APPROVED" | "REJECTED" | "PAID" | "CANCELED" | null) => {
    sharedState.stateFilter = state;
  };

  const updateDateRange = (range: Date[] | null) => {
    sharedState.dateRange = range;
  };

  const resetFilters = () => {
    sharedState.typeFilter = null;
    sharedState.stateFilter = null;
    const today = new Date();
    sharedState.dateRange = [today, today];
  };

  const getDefaultDateRange = () => {
    const today = new Date();
    return [today, today];
  };

  return {
    // State (reactive references to shared state)
    typeFilter: computed(() => sharedState.typeFilter),
    stateFilter: computed(() => sharedState.stateFilter),
    dateRange: computed({
      get: () => sharedState.dateRange,
      set: (value) => { sharedState.dateRange = value; }
    }),
    
    // Computed
    currentFilterStatus,
    
    // Methods
    buildFilters,
    updateTypeFilter,
    updateStateFilter,
    updateDateRange,
    resetFilters,
    getDefaultDateRange,
  };
}