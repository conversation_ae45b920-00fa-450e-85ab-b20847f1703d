<template>
  <div class="inline-block">
    <!-- Tri<PERSON> - styled similar to DatePicker default -->
    <Button
      :label="buttonLabel"
      icon="pi pi-calendar"
      severity="secondary"
      variant="outlined"
      @click="togglePanel"
      aria-haspopup="dialog"
      aria-controls="date_range_overlay"
      :aria-expanded="isOpen"
    />

    <!-- Popover Panel with max width -->
    <Popover
      ref="panel"
      id="date_range_overlay"
      @hide="handlePanelHide"
      role="dialog"
      aria-modal="true"
      class="max-w-md p-0"
    >
      <DatePicker
        v-model="internalValue"
        selection-mode="range"
        :inline="true"
        :manual-input="false"
        date-format="dd/mm/yy"
        :show-week="true"
        :show-other-months="true"
      >
        <template #footer>
          <div class="flex flex-col gap-4 border-t border-gray-200 pt-4">
            <!-- Preset Buttons with flex-wrap -->
            <div class="flex flex-wrap gap-2">
              <Button
                v-for="preset in presets"
                :key="preset.key"
                :label="preset.label"
                size="small"
                :severity="activePreset === preset.key ? 'primary' : 'secondary'"
                @click="applyPreset(preset)"
              />
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end gap-2">
              <Button label="Huỷ" severity="secondary" @click="cancelSelection" />
              <Button label="Áp dụng" @click="applySelection" :disabled="!hasValidSelection" />
            </div>
          </div>
        </template>
      </DatePicker>
    </Popover>
  </div>
</template>

<script setup lang="ts">
import { watch } from "vue";
import { useDateRangePicker } from "@/composables/useDateRangePicker";

// Props & defineModel
interface Props {
  placeholder?: string;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: "Chọn khoảng thời gian",
  disabled: false,
});

const modelValue = defineModel<Date[] | null>({ default: null });

// Use the composable
const {
  panel,
  internalValue,
  activePreset,
  isOpen,
  presets,
  buttonLabel,
  hasValidSelection,
  togglePanel,
  closePanel,
  handlePanelHide,
  applyPreset,
  updateValue,
} = useDateRangePicker({
  initialValue: modelValue.value,
  placeholder: props.placeholder,
  onSelectionChange: (value) => {
    modelValue.value = value;
  },
});

// Apply/Cancel with model value
function applySelection() {
  if (!hasValidSelection.value) return;

  modelValue.value = internalValue.value;
  closePanel();
}

function cancelSelection() {
  updateValue(modelValue.value);
  closePanel();
}

// Watch for external changes
watch(
  modelValue,
  (newValue) => {
    // Only update if panel is not open to avoid overriding user selection
    if (!isOpen.value) {
      updateValue(newValue);
    }
  },
  { deep: true },
);
</script>
