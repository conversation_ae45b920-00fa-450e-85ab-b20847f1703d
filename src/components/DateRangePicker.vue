<template>
  <div class="inline-block">
    <!-- Trigger styled like DatePicker input with icon -->
    <div
      class="p-datepicker p-component p-inputwrapper"
      :class="{ 'p-inputwrapper-focus': isOpen }"
    >
      <input
        type="text"
        :value="buttonLabel"
        readonly
        :placeholder="props.placeholder"
        :disabled="props.disabled"
        class="p-inputtext p-component"
        :class="{
          'p-disabled': props.disabled,
          'p-variant-filled': props.variant === 'filled'
        }"
        @click="!props.disabled && togglePanel($event)"
        aria-haspopup="dialog"
        aria-controls="date_range_overlay"
        :aria-expanded="isOpen"
      />
      <button
        v-if="!props.disabled"
        type="button"
        class="p-datepicker-trigger p-button p-button-icon-only p-button-text"
        @click="togglePanel($event)"
        aria-haspopup="dialog"
        aria-controls="date_range_overlay"
        :aria-expanded="isOpen"
      >
        <span class="p-button-icon pi pi-calendar" aria-hidden="true"></span>
      </button>
    </div>

    <!-- Popover Panel with max width -->
    <Popover
      ref="panel"
      id="date_range_overlay"
      @hide="handlePanelHide"
      role="dialog"
      aria-modal="true"
      class="max-w-md p-0"
    >
      <DatePicker
        v-model="internalValue"
        selection-mode="range"
        :inline="true"
        :manual-input="false"
        date-format="dd/mm/yy"
        :show-week="true"
        :show-other-months="true"
      >
        <template #footer>
          <div class="flex flex-col gap-4 border-t border-gray-200 pt-4">
            <!-- Preset Buttons with flex-wrap -->
            <div class="flex flex-wrap gap-2">
              <Button
                v-for="preset in presets"
                :key="preset.key"
                :label="preset.label"
                size="small"
                :severity="activePreset === preset.key ? 'primary' : 'secondary'"
                @click="applyPreset(preset)"
              />
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end gap-2">
              <Button label="Huỷ" severity="secondary" @click="cancelSelection" />
              <Button label="Áp dụng" @click="applySelection" :disabled="!hasValidSelection" />
            </div>
          </div>
        </template>
      </DatePicker>
    </Popover>
  </div>
</template>

<script setup lang="ts">
import { watch } from "vue";
import { useDateRangePicker } from "@/composables/useDateRangePicker";

// Props & defineModel
interface Props {
  placeholder?: string;
  disabled?: boolean;
  variant?: 'outlined' | 'filled';
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: "Chọn khoảng thời gian",
  disabled: false,
  variant: 'outlined',
});

const modelValue = defineModel<Date[] | null>({ default: null });

// Use the composable
const {
  panel,
  internalValue,
  activePreset,
  isOpen,
  presets,
  buttonLabel,
  hasValidSelection,
  togglePanel,
  closePanel,
  handlePanelHide,
  applyPreset,
  updateValue,
} = useDateRangePicker({
  initialValue: modelValue.value,
  placeholder: props.placeholder,
  onSelectionChange: (value) => {
    modelValue.value = value;
  },
});

// Apply/Cancel with model value
function applySelection() {
  if (!hasValidSelection.value) return;

  modelValue.value = internalValue.value;
  closePanel();
}

function cancelSelection() {
  updateValue(modelValue.value);
  closePanel();
}

// Watch for external changes
watch(
  modelValue,
  (newValue) => {
    // Only update if panel is not open to avoid overriding user selection
    if (!isOpen.value) {
      updateValue(newValue);
    }
  },
  { deep: true },
);
</script>

<style scoped>
/* Ensure the DateRangePicker trigger matches PrimeVue DatePicker styling */
.p-datepicker {
  position: relative;
  display: inline-flex;
  width: 100%;
}

.p-datepicker .p-inputtext {
  flex: 1 1 auto;
  width: 1%;
  padding-right: 2.5rem;
}

.p-datepicker .p-datepicker-trigger {
  position: absolute;
  top: 50%;
  right: 0.75rem;
  transform: translateY(-50%);
  width: 1.5rem;
  height: 1.5rem;
  border: 0;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--p-text-muted-color);
  transition: color 0.2s;
}

.p-datepicker .p-datepicker-trigger:hover {
  color: var(--p-text-color);
}

.p-datepicker .p-datepicker-trigger:focus {
  outline: 0;
  outline-offset: 0;
  box-shadow: var(--p-focus-ring);
}

.p-datepicker .p-datepicker-trigger .p-button-icon {
  font-size: 1rem;
}

/* Ensure proper cursor for readonly input */
.p-datepicker .p-inputtext[readonly] {
  cursor: pointer;
}

/* Disabled state */
.p-datepicker .p-inputtext.p-disabled {
  cursor: default;
}

.p-datepicker .p-inputtext.p-disabled + .p-datepicker-trigger {
  cursor: default;
  color: var(--p-text-muted-color);
  opacity: 0.6;
}
</style>
